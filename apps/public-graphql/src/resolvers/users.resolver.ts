import { Context, Parent, Query, ResolveField, Resolver, Mutation, Field, ObjectType } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { User } from 'libs/internal/users/entities/user.entity';
import { UsersService } from 'libs/internal/users/users.service';
import { GqlAuthGuard } from 'libs/internal/auth/auth.guard';
import { UserManagedWalletDTO } from 'libs/internal/users/dto/managed-wallet.dto';
import { UserEmbeddedWalletDTO } from 'libs/internal/users/dto/embedded-wallet.dto';

@ObjectType()
export class MarkAccountDeleteResponse {
    @Field(() => Boolean)
    success: boolean;

    @Field({ nullable: true })
    deletedAt?: Date;
}

@Resolver(() => User)
export class UsersResolver {
    constructor(private readonly usersService: UsersService) {}

    @UseGuards(GqlAuthGuard)
    @Query(() => User)
    async account(@Context() context: any): Promise<User> {
        return this.usersService.getUser(context.req.user.sub);
    }

    @ResolveField(() => [UserManagedWalletDTO])
    async userManagedWallets(@Parent() user: User) {
        return this.usersService.getUserManagedWallets(user);
    }

    @ResolveField(() => [UserEmbeddedWalletDTO])
    async userEmbeddedWallets(@Parent() user: User) {
        return this.usersService.getUserEmbeddedWallets(user);
    }

    @UseGuards(GqlAuthGuard)
    @Mutation(() => MarkAccountDeleteResponse)
    async markAccountDelete(@Context() context: any): Promise<MarkAccountDeleteResponse> {
        const user = await this.usersService.markAccountDelete(context.req.user.sub);
        return {
            success: true,
            deletedAt: user.deletedAt,
        };
    }
}
