import { Test, TestingModule } from '@nestjs/testing';
import { UsersService } from '../users.service';
import { User } from '../entities/user.entity';
import { AuthProvider, TurnkeyVersion } from '../entities/user.entity';

describe('MarkAccountDelete', () => {
    let usersService: UsersService;
    let mockUser: User;

    beforeEach(async () => {
        // Create a mock user
        mockUser = new User();
        mockUser.id = 'test-user-id';
        mockUser.authProvider = AuthProvider.CHAIN_EVM;
        mockUser.walletAddress = '0x123...';
        mockUser.referrerCode = 'TEST123';
        mockUser.language = 'en';
        mockUser.isFirstLogin = false;
        mockUser.turnkeyVersion = TurnkeyVersion.V1;
        mockUser.deletedAt = undefined;

        // Mock UsersService
        const mockUsersService = {
            getUserById: jest.fn().mockResolvedValue(mockUser),
            updateUser: jest.fn().mockResolvedValue(undefined),
            markAccountDelete: jest.fn().mockImplementation(async (userId: string) => {
                if (userId === mockUser.id) {
                    mockUser.deletedAt = new Date();
                    return mockUser;
                }
                throw new Error('User not found');
            }),
        };

        const module: TestingModule = await Test.createTestingModule({
            providers: [
                {
                    provide: UsersService,
                    useValue: mockUsersService,
                },
            ],
        }).compile();

        usersService = module.get<UsersService>(UsersService);
    });

    describe('markAccountDelete', () => {
        it('should mark user account as deleted', async () => {
            // Arrange
            expect(mockUser.deletedAt).toBeUndefined();

            // Act
            const result = await usersService.markAccountDelete(mockUser.id);

            // Assert
            expect(result.deletedAt).toBeDefined();
            expect(result.deletedAt).toBeInstanceOf(Date);
            expect(result.id).toBe(mockUser.id);
        });

        it('should throw error for non-existent user', async () => {
            // Act & Assert
            await expect(usersService.markAccountDelete('non-existent-id')).rejects.toThrow('User not found');
        });
    });

    describe('User entity deletedAt field', () => {
        it('should have deletedAt field as optional Date', () => {
            // Arrange
            const user = new User();

            // Assert
            expect(user.deletedAt).toBeUndefined();

            // Test setting deletedAt
            const now = new Date();
            user.deletedAt = now;
            expect(user.deletedAt).toBe(now);
        });
    });
});
